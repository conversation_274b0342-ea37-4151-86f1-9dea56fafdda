import requests
from bs4 import <PERSON><PERSON>oup
import json
import time
from datetime import datetime, timedelta
import re
import os
from urllib.parse import urljoin, urlparse
import sqlite3

class AdvancedDataCollector:
    def __init__(self, db_path="data_collection.db"):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for storing collected data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables for different data sources
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trending_topics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                source TEXT NOT NULL,
                url TEXT,
                score INTEGER DEFAULT 0,
                comments INTEGER DEFAULT 0,
                keywords TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed BOOLEAN DEFAULT FALSE
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS content_ideas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content_type TEXT,
                keywords TEXT,
                priority_score INTEGER DEFAULT 0,
                source_topic_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                generated BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (source_topic_id) REFERENCES trending_topics (id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS search_queries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT NOT NULL,
                search_volume INTEGER DEFAULT 0,
                competition TEXT,
                related_keywords TEXT,
                collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def scrape_reddit_advanced(self, subreddits=None, time_filter='day', limit=25):
        """Enhanced Reddit scraping with better data extraction"""
        if subreddits is None:
            subreddits = [
                'entrepreneur', 'business', 'artificial', 'MachineLearning',
                'productivity', 'marketing', 'SaaS', 'startups', 'passive_income',
                'automation', 'digitalnomad', 'freelance', 'sidehustle'
            ]
        
        collected_data = []
        
        for subreddit in subreddits:
            try:
                # Try different sorting methods
                for sort in ['hot', 'top']:
                    url = f"https://www.reddit.com/r/{subreddit}/{sort}.json"
                    params = {'limit': limit, 't': time_filter}
                    
                    response = requests.get(url, headers=self.headers, params=params)
                    
                    if response.status_code == 200:
                        data = response.json()
                        posts = data['data']['children']
                        
                        for post in posts:
                            post_data = post['data']
                            
                            # Extract keywords from title and selftext
                            keywords = self._extract_keywords(
                                post_data['title'] + ' ' + post_data.get('selftext', '')
                            )
                            
                            topic_data = {
                                'title': post_data['title'],
                                'source': f"reddit_r_{subreddit}",
                                'url': f"https://reddit.com{post_data['permalink']}",
                                'score': post_data['score'],
                                'comments': post_data['num_comments'],
                                'keywords': json.dumps(keywords),
                                'selftext': post_data.get('selftext', '')[:500],  # First 500 chars
                                'created_utc': post_data['created_utc']
                            }
                            
                            collected_data.append(topic_data)
                    
                    time.sleep(1)  # Rate limiting
                
            except Exception as e:
                print(f"Error scraping r/{subreddit}: {e}")
                continue
        
        return collected_data
    
    def scrape_quora_questions(self, topics=None):
        """Scrape Quora for trending questions (limited due to anti-bot measures)"""
        if topics is None:
            topics = ['AI', 'automation', 'business', 'marketing', 'productivity']
        
        collected_data = []
        
        for topic in topics:
            try:
                # Quora search URL
                url = f"https://www.quora.com/search?q={topic.replace(' ', '+')}"
                
                response = requests.get(url, headers=self.headers)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Look for question links (this may need adjustment based on Quora's current structure)
                    question_links = soup.find_all('a', href=re.compile(r'/[^/]+/answer/'))
                    
                    for link in question_links[:10]:  # Limit to 10 per topic
                        question_text = link.get_text(strip=True)
                        if question_text and len(question_text) > 10:
                            keywords = self._extract_keywords(question_text)
                            
                            topic_data = {
                                'title': question_text,
                                'source': f"quora_{topic}",
                                'url': urljoin('https://www.quora.com', link.get('href', '')),
                                'score': 0,  # Quora doesn't expose scores easily
                                'comments': 0,
                                'keywords': json.dumps(keywords)
                            }
                            
                            collected_data.append(topic_data)
                
                time.sleep(2)  # Longer delay for Quora
                
            except Exception as e:
                print(f"Error scraping Quora for {topic}: {e}")
                continue
        
        return collected_data
    
    def scrape_hackernews_enhanced(self, num_stories=50):
        """Enhanced Hacker News scraping with better categorization"""
        collected_data = []
        
        try:
            # Get top stories
            url = "https://hacker-news.firebaseio.com/v0/topstories.json"
            response = requests.get(url)
            
            if response.status_code == 200:
                story_ids = response.json()[:num_stories]
                
                for story_id in story_ids:
                    story_url = f"https://hacker-news.firebaseio.com/v0/item/{story_id}.json"
                    story_response = requests.get(story_url)
                    
                    if story_response.status_code == 200:
                        story_data = story_response.json()
                        
                        if story_data and 'title' in story_data:
                            keywords = self._extract_keywords(story_data['title'])
                            
                            topic_data = {
                                'title': story_data['title'],
                                'source': 'hackernews',
                                'url': story_data.get('url', f"https://news.ycombinator.com/item?id={story_id}"),
                                'score': story_data.get('score', 0),
                                'comments': story_data.get('descendants', 0),
                                'keywords': json.dumps(keywords),
                                'story_type': story_data.get('type', 'story')
                            }
                            
                            collected_data.append(topic_data)
                    
                    time.sleep(0.1)  # Rate limiting
                    
        except Exception as e:
            print(f"Error scraping Hacker News: {e}")
        
        return collected_data
    
    def scrape_twitter_trends(self):
        """
        Placeholder for Twitter trends scraping
        Note: Twitter API requires authentication, this would need proper API setup
        """
        # This would require Twitter API v2 access
        # For now, return empty list
        print("Twitter scraping requires API authentication - skipping for demo")
        return []
    
    def analyze_search_trends(self, keywords):
        """
        Analyze search trends for given keywords
        This is a placeholder - would integrate with Google Trends API or similar
        """
        search_data = []
        
        for keyword in keywords:
            # Mock search volume data
            search_data.append({
                'keyword': keyword,
                'search_volume': self._estimate_search_volume(keyword),
                'competition': self._estimate_competition(keyword),
                'related_keywords': self._generate_related_keywords(keyword)
            })
        
        return search_data
    
    def _extract_keywords(self, text):
        """Extract relevant keywords from text"""
        # Clean and tokenize text
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Filter out common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'how', 'what', 'why', 'when', 'where', 'is', 'are',
            'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that',
            'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
            'her', 'us', 'them', 'my', 'your', 'his', 'its', 'our', 'their'
        }
        
        # Target keywords related to our niche
        target_keywords = {
            'ai', 'artificial', 'intelligence', 'automation', 'machine', 'learning',
            'business', 'startup', 'entrepreneur', 'marketing', 'seo', 'productivity',
            'tools', 'software', 'saas', 'revenue', 'growth', 'strategy', 'digital',
            'online', 'passive', 'income', 'content', 'blog', 'website', 'traffic',
            'conversion', 'sales', 'profit', 'money', 'earn', 'make', 'build', 'create'
        }
        
        # Extract relevant keywords
        keywords = []
        for word in words:
            if (len(word) > 3 and 
                word not in stop_words and 
                (word in target_keywords or any(target in word for target in target_keywords))):
                keywords.append(word)
        
        # Remove duplicates and return top 10
        return list(dict.fromkeys(keywords))[:10]
    
    def _estimate_search_volume(self, keyword):
        """Estimate search volume (mock implementation)"""
        # This would integrate with actual search volume APIs
        base_volumes = {
            'ai': 50000, 'automation': 30000, 'business': 80000,
            'marketing': 60000, 'seo': 40000, 'productivity': 25000
        }
        
        for key, volume in base_volumes.items():
            if key in keyword.lower():
                return volume + hash(keyword) % 10000
        
        return hash(keyword) % 5000 + 1000
    
    def _estimate_competition(self, keyword):
        """Estimate keyword competition (mock implementation)"""
        competition_levels = ['Low', 'Medium', 'High']
        return competition_levels[hash(keyword) % 3]
    
    def _generate_related_keywords(self, keyword):
        """Generate related keywords (mock implementation)"""
        prefixes = ['best', 'how to', 'free', 'top', 'automated']
        suffixes = ['tools', 'software', 'guide', 'tips', 'strategies', 'examples']
        
        related = []
        for prefix in prefixes[:2]:
            related.append(f"{prefix} {keyword}")
        for suffix in suffixes[:2]:
            related.append(f"{keyword} {suffix}")
        
        return related
    
    def save_to_database(self, data, table_name):
        """Save collected data to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if table_name == 'trending_topics':
            for item in data:
                cursor.execute('''
                    INSERT INTO trending_topics 
                    (title, source, url, score, comments, keywords)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    item['title'], item['source'], item['url'],
                    item['score'], item['comments'], item['keywords']
                ))
        
        conn.commit()
        conn.close()
    
    def get_unprocessed_topics(self, limit=10):
        """Get unprocessed topics from database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, title, source, keywords, score
            FROM trending_topics
            WHERE processed = FALSE
            ORDER BY score DESC, collected_at DESC
            LIMIT ?
        ''', (limit,))
        
        topics = cursor.fetchall()
        conn.close()
        
        return topics
    
    def mark_topic_processed(self, topic_id):
        """Mark a topic as processed"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE trending_topics
            SET processed = TRUE
            WHERE id = ?
        ''', (topic_id,))
        
        conn.commit()
        conn.close()
    
    def run_full_collection(self):
        """Run complete data collection workflow"""
        print("[INFO] Starting comprehensive data collection...")
        
        all_data = []
        
        # Collect from Reddit
        print("Collecting from Reddit...")
        reddit_data = self.scrape_reddit_advanced()
        all_data.extend(reddit_data)
        print(f"[SUCCESS] Collected {len(reddit_data)} items from Reddit")
        
        # Collect from Hacker News
        print("Collecting from Hacker News...")
        hn_data = self.scrape_hackernews_enhanced()
        all_data.extend(hn_data)
        print(f"[SUCCESS] Collected {len(hn_data)} items from Hacker News")
        
        # Collect from Quora (limited)
        print("Collecting from Quora...")
        quora_data = self.scrape_quora_questions()
        all_data.extend(quora_data)
        print(f"[SUCCESS] Collected {len(quora_data)} items from Quora")
        
        # Save to database
        if all_data:
            self.save_to_database(all_data, 'trending_topics')
            print(f"[SAVED] Saved {len(all_data)} items to database")
        
        # Generate content ideas
        unprocessed = self.get_unprocessed_topics(20)
        content_ideas = self._generate_content_ideas_from_topics(unprocessed)
        
        print(f"[IDEAS] Generated {len(content_ideas)} content ideas")
        
        return {
            'total_collected': len(all_data),
            'reddit_items': len(reddit_data),
            'hackernews_items': len(hn_data),
            'quora_items': len(quora_data),
            'content_ideas': len(content_ideas)
        }
    
    def _generate_content_ideas_from_topics(self, topics):
        """Generate content ideas from collected topics"""
        content_ideas = []
        
        for topic_id, title, source, keywords_json, score in topics:
            try:
                keywords = json.loads(keywords_json) if keywords_json else []
            except:
                keywords = []
            
            # Generate different types of content ideas
            ideas = []
            
            if any(kw in keywords for kw in ['ai', 'automation', 'artificial']):
                ideas.extend([
                    f"How to Use AI for {self._extract_main_concept(title)}",
                    f"Automating {self._extract_main_concept(title)}: Complete Guide",
                    f"AI Tools for {self._extract_main_concept(title)} in 2025"
                ])
            
            if any(kw in keywords for kw in ['business', 'startup', 'entrepreneur']):
                ideas.extend([
                    f"Building a Profitable {self._extract_main_concept(title)} Business",
                    f"{self._extract_main_concept(title)}: From Zero to $10K/Month",
                    f"The Ultimate {self._extract_main_concept(title)} Strategy"
                ])
            
            for idea in ideas:
                content_ideas.append({
                    'title': idea,
                    'content_type': self._determine_content_type(idea),
                    'keywords': keywords,
                    'priority_score': score,
                    'source_topic_id': topic_id
                })
            
            # Mark topic as processed
            self.mark_topic_processed(topic_id)
        
        return content_ideas
    
    def _extract_main_concept(self, title):
        """Extract main concept from title"""
        # Simple extraction logic
        words = title.split()
        # Remove common words and return key concept
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        key_words = [w for w in words if w.lower() not in stop_words and len(w) > 3]
        return ' '.join(key_words[:3]) if key_words else title[:30]
    
    def _determine_content_type(self, title):
        """Determine content type based on title"""
        title_lower = title.lower()
        
        if 'guide' in title_lower or 'how to' in title_lower:
            return 'guide'
        elif 'template' in title_lower or 'checklist' in title_lower:
            return 'template'
        elif 'strategy' in title_lower:
            return 'strategy_guide'
        elif 'tools' in title_lower:
            return 'tool_review'
        else:
            return 'article'

# Example usage
if __name__ == "__main__":
    collector = AdvancedDataCollector()
    
    # Run full collection
    results = collector.run_full_collection()
    
    print("\n[SUMMARY] Collection Summary:")
    for key, value in results.items():
        print(f"{key}: {value}")
    
    # Show some unprocessed topics
    unprocessed = collector.get_unprocessed_topics(5)
    print(f"\n[TOPICS] Sample unprocessed topics:")
    for topic in unprocessed:
        print(f"- {topic[1]} (Score: {topic[4]})")

