# AI Automation Hub - Local Setup Guide

This guide provides comprehensive instructions on how to set up and run the AI Automation Hub project on your local machine. The project consists of two main components: a React frontend (AI Automation Hub) and a Flask backend (Automation API).

## 1. Prerequisites

Before you begin, ensure you have the following software installed on your local machine:

### 1.1 Node.js and npm

Node.js is a JavaScript runtime environment, and npm (Node Package Manager) is used for managing JavaScript packages. The React frontend requires Node.js and npm.

- **Download**: Visit the official Node.js website: [https://nodejs.org/en/download/](https://nodejs.org/en/download/)
- **Installation**: Follow the instructions for your operating system. npm is typically installed automatically with Node.js.
- **Verification**: Open your terminal or command prompt and run the following commands to verify the installation:

```bash
node -v
npm -v
```

### 1.2 Python and pip

Python is required for the Flask backend and various automation scripts. pip is the package installer for Python.

- **Download**: Visit the official Python website: [https://www.python.org/downloads/](https://www.python.org/downloads/)
- **Installation**: Follow the instructions for your operating system. Ensure you check the option to 


add Python to your PATH during installation.
- **Verification**: Open your terminal or command prompt and run the following commands to verify the installation:

```bash
python3 --version
pip3 --version
```

### 1.3 Git

Git is a version control system used for cloning the project repository. While the project is provided as a zip file, having Git installed is good practice for future development.

- **Download**: Visit the official Git website: [https://git-scm.com/downloads](https://git-scm.com/downloads)
- **Installation**: Follow the instructions for your operating system.
- **Verification**: Open your terminal or command prompt and run the following command:

```bash
git --version
```

## 2. Project Setup

Once you have the prerequisites installed, follow these steps to set up the project.

### 2.1 Extract the Project Archive

Extract the `ai_automation_hub_project.zip` file to a directory of your choice. This will create a folder named `ai_automation_hub_project` (or similar, depending on your extraction tool) containing all the project files.

For the purpose of this guide, let's assume you extract it to your home directory, resulting in a path like `/home/<USER>/ai_automation_hub_project/`.

### 2.2 Navigate to the Project Directory

Open your terminal or command prompt and navigate to the extracted project directory:

```bash
cd /path/to/your/extracted/ai_automation_hub_project
```

Replace `/path/to/your/extracted/` with the actual path where you extracted the zip file.

## 3. Frontend Setup (React App)

The frontend is a React application located in the `ai-automation-hub` directory within the main project folder.

### 3.1 Install Dependencies

Navigate into the frontend directory and install the necessary Node.js packages:

```bash
cd ai-automation-hub
npm install
```

This command reads the `package.json` file and installs all the required dependencies for the React application. This might take a few minutes.

### 3.2 Run the Frontend Application

After installing dependencies, you can start the React development server:

```bash
npm run dev
```

This will start the development server, usually on `http://localhost:5173`. Open your web browser and navigate to this URL to see the AI Automation Hub website running locally. The terminal will display the local URL once the server is ready.

To stop the frontend application, press `Ctrl + C` in the terminal where it's running.

## 4. Backend Setup (Flask App)

The backend is a Flask application located in the `automation-api` directory within the main project folder.

### 4.1 Create a Virtual Environment

It's highly recommended to use a virtual environment to manage Python dependencies. This isolates the project's dependencies from your system-wide Python installation.

Navigate into the backend directory:

```bash
cd ../automation-api
```

Now, create a virtual environment:

```bash
python3 -m venv venv
```

### 4.2 Activate the Virtual Environment

Activate the virtual environment. The command varies slightly depending on your operating system:

- **macOS/Linux**:

```bash
source venv/bin/activate
```

- **Windows (Command Prompt)**:

```bash
venc\Scripts\activate.bat
```

- **Windows (PowerShell)**:

```bash
venc\Scripts\Activate.ps1
```

You will see `(venv)` prepended to your terminal prompt, indicating that the virtual environment is active.

### 4.3 Install Dependencies

With the virtual environment activated, install the Python dependencies using pip:

```bash
pip install -r requirements.txt
```

This command installs all the required Python packages listed in `requirements.txt` for the Flask application.

### 4.4 Run the Backend Application

After installing dependencies, you can start the Flask development server:

```bash
flask run --host=0.0.0.0
```

This will start the Flask server, usually on `http://127.0.0.1:5000` or `http://localhost:5000`. The `--host=0.0.0.0` flag makes the server accessible from other devices on your local network, which is useful for testing. The terminal will display the local URL once the server is ready.

To stop the backend application, press `Ctrl + C` in the terminal where it's running.

## 5. Running Both Frontend and Backend

To run the complete AI Automation Hub locally, you need to run both the frontend and backend applications concurrently. This means you will need two separate terminal windows.

**Terminal 1 (Frontend)**:

```bash
cd /path/to/your/extracted/ai_automation_hub_project/ai-automation-hub
npm run dev
```

**Terminal 2 (Backend)**:

```bash
cd /path/to/your/extracted/ai_automation_hub_project/automation-api
source venv/bin/activate  # or appropriate activation command for Windows
flask run --host=0.0.0.0
```

Once both servers are running, you can access the full AI Automation Hub website by navigating to the frontend URL (e.g., `http://localhost:5173`) in your web browser. The frontend will communicate with the backend API as needed.

## 6. Troubleshooting

Here are some common issues and their solutions:

### 6.1 Port Already in Use

If you encounter an error indicating that a port is already in use (e.g., `EADDRINUSE`), it means another application is using that port. You can either:

- **Change the port**: For the React app, you can usually specify a different port by setting the `PORT` environment variable before running `npm run dev` (e.g., `PORT=3001 npm run dev`). For Flask, you can use `flask run --port=5001`.
- **Find and kill the process**: On Linux/macOS, you can use `lsof -i :<port_number>` to find the process using the port and then `kill <PID>` to terminate it. On Windows, you can use `netstat -ano | findstr :<port_number>` and then `taskkill /PID <PID> /F`.

### 6.2 Missing Dependencies

If you get errors about missing modules or packages, ensure you have installed all dependencies correctly:

- **Frontend**: Navigate to `ai-automation-hub` and run `npm install`.
- **Backend**: Navigate to `automation-api`, activate your virtual environment, and run `pip install -r requirements.txt`.

### 6.3 Frontend Not Connecting to Backend

If the frontend loads but data from the backend is not displayed, check the following:

- **Backend running**: Ensure the Flask backend is running and accessible at `http://localhost:5000` (or the port it's configured to use).
- **CORS**: The Flask backend is configured with CORS, but if you modify it, ensure CORS is still enabled to allow requests from the frontend's origin.
- **Network issues**: Check your firewall settings if you are having trouble connecting.

### 6.4 Python Virtual Environment Issues

If you have trouble activating or using the virtual environment:

- **Recreate**: You can always delete the `venv` folder and recreate it by following steps 4.1 and 4.2.
- **Correct activation command**: Double-check that you are using the correct activation command for your operating system and shell.

## 7. Project Structure

Here's a brief overview of the project structure:

```
ai_automation_hub_project/
├── ai-automation-hub/          # React Frontend Application
│   ├── public/
│   ├── src/
│   │   ├── components/         # Reusable React components
│   │   ├── pages/              # Individual website pages
│   │   ├── App.jsx             # Main React application file
│   │   └── main.jsx            # Entry point for React app
│   ├── index.html
│   ├── package.json            # Frontend dependencies
│   └── vite.config.js
├── automation-api/             # Flask Backend Application
│   ├── src/
│   │   ├── models/             # Database models
│   │   ├── routes/             # API route definitions
│   │   ├── static/             # Static files (e.g., dashboard HTML)
│   │   └── main.py             # Main Flask application file
│   ├── venv/                   # Python virtual environment
│   └── requirements.txt        # Backend dependencies
├── *.py                        # Various Python automation scripts
├── *.md                        # Documentation and reports
├── *.txt                       # Other configuration files
└── ai_automation_hub_project.zip # The project archive itself
```

## 8. Conclusion

By following these instructions, you should have the AI Automation Hub project up and running on your local machine. This setup provides a powerful platform for building and managing automated AI businesses. If you encounter any further issues, please refer to the troubleshooting section or consult online resources for Node.js, React, and Flask development.

---

*Guide generated by Manus AI on June 23, 2025*

